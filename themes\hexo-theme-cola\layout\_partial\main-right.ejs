<div class="main-right">
  <div class="main-right--board">
    <div class="main-right--title">
      <h5>公告栏</h5>
      <i class="iconfont icon-gonggao"></i>
    </div>
    <div class="main-right--content">
      <%- theme.notice %> 
    </div>
  </div>

  <!-- 音乐播放器已禁用 -->

  <div class="operate-items">
    <div class="operate-item backtop">
      <i class="iconfont icon-huidaodingbu"></i>
      <span>回到顶部</span>
    </div>
    <% if (theme.comments.enable) { %>
    <div class="operate-item turn-comment hidden">
      <i class="iconfont icon-pinglun"></i>
      <span>查看评论</span>
    </div>
    <% } %>
  </div>

  <div class="main-right--site">
    <div class="main-right--power">
      <p>Power By <a href="https://hexo.io/zh-cn/docs/">Hexo</a>.</p>
      <p>Theme：<a href="https://github.com/Aizener/hexo-theme-cola">Cola.</a></p>
    </div>
    <p class="main-right--refer"><a href="https://beian.miit.gov.cn/#/Integrated/index"><%- theme.copyright %></a> </p>
  </div>
</div>
<%
  // 音乐功能已禁用
  const musics = []
%>
<script>
  function setOperateItem () {
    const reg = /\d{4}\/\d{2}\/\d{2}\/.+/
    const path = location.pathname
    const operateDom = document.querySelector('.main-right .operate-items')
    const commentDom = document.querySelector('.turn-comment')
    const cateloguDom = document.querySelector('.article-catelogue > .article-catelogue--wrapper');

    if (commentDom) {
      if (reg.test(path) || path.match(/\/log\/.+/)) {
        commentDom.classList.remove('hidden')
        const newDom = operateDom.cloneNode(true);
        const _backtopDom = newDom.querySelector('.backtop');
        const _commentDom = newDom.querySelector('.turn-comment');
        _backtopDom.addEventListener('click', () => backTopEvent());
        _commentDom.addEventListener('click', () => commentDomEvent());
        cateloguDom.appendChild(newDom);
      } else {
        commentDom.classList.add('hidden')
      }
    }
  }

  setOperateItem()
  // 音乐播放器已禁用
  // const musics = JSON.parse(`<%- JSON.stringify(musics) %>`)
  // const ap = new APlayer({
  //   container: document.querySelector('#aplayer'),
  //   audio: musics,
  // })

  $(document).on('pjax:complete', function() {
    setOperateItem()
  })

  document.querySelector('.backtop').addEventListener('click', () => {
    backTopEvent();
  })
  const dom = document.querySelector('.turn-comment')
  dom && dom.addEventListener('click', () => {
    commentDomEvent();
  })

  function backTopEvent() {
    document.documentElement.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  function commentDomEvent() {
    const commentDom = document.querySelector('.comments-intro')
    if (!commentDom) return
    const top = commentDom.offsetTop, height = commentDom.offsetHeight
    document.documentElement.scrollTo({
      top: top - 2 * height,
      behavior: 'smooth'
    })
  }
</script>