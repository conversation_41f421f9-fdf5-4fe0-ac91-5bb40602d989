// 主题切换器调试脚本
// 在浏览器控制台中执行此脚本来调试主题切换功能

console.log('=== 主题切换器调试开始 ===');

// 1. 检查主题切换器元素
const switcher = document.getElementById('theme-switcher') || document.querySelector('.theme-switcher');
console.log('主题切换器元素:', switcher);

if (!switcher) {
  console.error('❌ 未找到主题切换器元素');
  console.log('页面中的所有元素:', document.querySelectorAll('*'));
} else {
  console.log('✅ 找到主题切换器元素');
  
  // 2. 检查子元素
  const toggle = switcher.querySelector('#theme-toggle') || switcher.querySelector('.theme-toggle');
  const panel = switcher.querySelector('#theme-panel') || switcher.querySelector('.theme-panel');
  const options = switcher.querySelectorAll('.theme-option');
  
  console.log('切换按钮:', toggle);
  console.log('主题面板:', panel);
  console.log('主题选项数量:', options.length);
  
  if (!toggle) {
    console.error('❌ 未找到切换按钮');
  }
  if (!panel) {
    console.error('❌ 未找到主题面板');
  }
  if (options.length === 0) {
    console.error('❌ 未找到主题选项');
  }
}

// 3. 检查CSS文件是否加载
const cssLinks = document.querySelectorAll('link[href*="background-themes"]');
console.log('主题CSS文件:', cssLinks);

// 4. 检查JavaScript文件是否加载
const jsScripts = document.querySelectorAll('script[src*="theme-switcher"]');
console.log('主题JS文件:', jsScripts);

// 5. 检查当前body类名
console.log('当前body类名:', document.body.className);

// 6. 手动测试主题切换
function testThemeSwitch(themeName) {
  console.log(`测试切换到主题: ${themeName}`);
  
  // 移除所有主题类
  const themeClasses = ['theme-tech-blue', 'theme-warm-orange', 'theme-forest-green', 'theme-sakura-pink', 'theme-deep-space', 'theme-sunset-yellow'];
  themeClasses.forEach(cls => document.body.classList.remove(cls));
  
  // 添加新主题类
  if (themeName !== 'default') {
    document.body.classList.add(`theme-${themeName}`);
  }
  
  console.log('切换后body类名:', document.body.className);
  
  // 检查容器背景
  setTimeout(() => {
    const container = document.querySelector('.container');
    if (container) {
      const computedStyle = window.getComputedStyle(container);
      console.log('容器背景样式:', computedStyle.background);
    }
  }, 100);
}

// 7. 提供测试函数
window.testTheme = testThemeSwitch;

console.log('=== 调试脚本加载完成 ===');
console.log('使用方法: testTheme("tech-blue") 来测试主题切换');
console.log('可用主题: default, tech-blue, warm-orange, forest-green, sakura-pink, deep-space, sunset-yellow');
