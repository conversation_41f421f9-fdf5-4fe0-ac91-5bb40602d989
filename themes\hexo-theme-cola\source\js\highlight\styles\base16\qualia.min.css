/*!
  Theme: Qualia
  Author: isaa<PERSON><PERSON><PERSON><PERSON>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:silver;background:#101010}.hljs ::selection,.hljs::selection{background-color:#454545;color:silver}.hljs-comment{color:#454545}.hljs-tag{color:grey}.hljs-operator,.hljs-punctuation,.hljs-subst{color:silver}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#efa6a2}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#a3b8ef}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#e6a3dc}.hljs-strong{font-weight:700;color:#e6a3dc}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#80c990}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#c8c874}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#50cacd}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#e0af85}.hljs-emphasis{color:#e0af85;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:grey}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}