@import '../var.styl';
@import '../markdown-theme.css';

.log {
  padding-bottom: 30px;
  .log-main-title {
    width: 100%;
    padding: 12px;
    text-align: center;
    margin: 12px 0;
    border: 1px solid #eee;
    background-color: rgba(240, 240, 240, .5);
    a {
      font-size: 18px;
      color: #1e90ff;
      &:hover {
        text-decoration: underline;
      }
    }
  }
  &-wrapper {
    &.more {
      overflow: hidden;
      position: relative;
      &::after {
        content: '';
        position: absolute;
        left: 50%;  
        bottom: 0;
        transform: translate(-50%, 0);
        width: 100%;
        height: 70px;
        opacity: .8;
        background: linear-gradient(to top, #333, transparent 80%, #ccc);
        filter: blur(10px);
      }
      .more-title {
        position: absolute;
        left: 50%; bottom: 10px;
        transform: translateX(-50%);
        color: #fff;
        font-size: 14px;
        z-index: 9;
        text-decoration: underline;
        transition: all .3s;
        &:hover {
          transform: translateX(-50%) translateY(-2px);
          text-decoration: underline;
          text-shadow: 0 1px 1px #eee, 0 2px 1px #ddd, 0 3px 1px #ccc, 0 4px 1px gray, 0 5px 1px #666;
        }
      }
    }
  }
  &-item {
    margin-top: 15px;
    padding: 15px;
    block-mixin();
    &:first-child {
      margin-top: 0;
    }
    h2.log-title {
      color: #1E90FF;
      font-size: 16px;
      font-weight: 400;
      padding-bottom: 10px;
      border-bottom: 1px solid #1E90FF;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

pre::-webkit-scrollbar {
  width: 5px;
  height: 10px;
  background-color:#F5F5F5;
}
/*定义滚动条轨道
内阴影+圆角*/
pre::-webkit-scrollbar-track {
  background-color:#F5F5F5;
}
/*定义滑块
内阴影+圆角*/
pre::-webkit-scrollbar-thumb {
  background-color: rgb(69, 83, 100);
}

pre:active {
  background-color: rgb(81, 95, 116);
}

@import '../highlight.css';