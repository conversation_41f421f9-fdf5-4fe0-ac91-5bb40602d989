<link rel="stylesheet" href="<%- url_for('css/partial/index.css') %>" />

<%
  const leftPosts = [], rightPosts = []
  page.per_page = 2
  page.posts.each(function(post, i) {
    if (i % 2 === 0) {
      leftPosts.push(post)
    } else {
      rightPosts.push(post)
    }
  })
%>

<div class="index">
  <div class="index-left">
    <% leftPosts.forEach(function(post){ %>
      <div class="index-article">
        <!-- <img class="index-article--cover" src="<%- url_for(post.cover || 'imgs/default-cover.webp') %>" alt=""> -->
        <div class="index-article--box">
          <img class="index-article--cover" data-cover="<%- post.cover || 'imgs/covers/default-cover.svg' %>" alt="<%- post.title %>" />
        </div>
        <div class="index-article--content">
          <a href="<%- url_for(post.path) %>"><%- post.title %> </a>
          <div>
            <% if (post.categories.length > 0) { %>
              <p>
                <%- list_categories(post.categories, {
                  show_count: false,
                  style: null,
                  class: 'post-category',
                  transform(str) {
                    return titlecase(str);
                  }
                }) %>
              </p>
            <% } %>
            <% if (post.tags.length > 0 ) { %>
              <p>
                <%- list_tags(post.tags, { class: '', order: -1, style: null, show_count: false, separator: '<span> ◆ </span>' }) %>
              </p>
            <% } %>
          </div>
          <div class="bg"></div>
        </div>
      </div>
    <% }) %>
  </div>
  <div class="index-center">
    <% rightPosts.forEach(function(post){ %>
      <div class="index-article">
        <!-- <img class="index-article--cover" data-cover="<%- post.cover %>" src="<%- url_for(post.cover || 'imgs/default-cover.webp') %>" alt=""> -->
        <div class="index-article--box">
          <img class="index-article--cover" data-cover="<%- post.cover || 'imgs/covers/default-cover.svg' %>" alt="<%- post.title %>" />
        </div>
        <div class="index-article--content">
          <a href="<%- url_for(post.path) %>"><%- post.title %> </a>
          <div>
            <% if (post.categories.length > 0) { %>
              <p>
                <%- list_categories(post.categories, {
                  show_count: false,
                  style: null,
                  transform(str) {
                    return titlecase(str)
                  }
                }) %>
              </p>
            <% } %>
            <% if (post.tags.length > 0 ) { %>
              <p>
                <%- list_tags(post.tags, { class: '', order: -1, style: null, show_count: false, separator: '<span> ◆ </span>' }) %>
              </p>
            <% } %>
          </div>
          <div class="bg"></div>
        </div>
      </div>
    <% }) %>
  </div>
  <div class="index-right">
    <div class="index-right-wrapper">
      <%- list_categories(site.categories, {
        show_count: false,
        transform(str) {
          return titlecase(str)
        },
      }) %>
      <%- list_tags(site.tags, { class: { ul: 'tags' }, order: -1, show_count: false }) %>
      <%- list_archives({ order: -1, format: 'YYYY年MM月', transform(str) {
        return str
      } }) %>
    </div>
  </div>
</div>
<% if (site.posts.length > 10) { %>
  <div class="page">
    <%- paginator({
      total: page.total,
      prev_text: '<',
      next_text: '>',
    }) %>
  </div>
<% } %>

<script>
  var allImgBoxs = document.querySelectorAll('.index-article--box');
  var windowHeight = window.innerHeight;
  var boxs = Array.from(allImgBoxs);
  var timer = null;
  boxs.forEach((box, idx) => {
    if (!window.loadImageSet || !window.loadImageSet.includes(idx)) {
      box.classList.add('img-loading');
    } else {
      const imgDom = box.querySelector('img');
      imgDom.src = imgDom.dataset.cover;
    }
  });

  function scrollFn() {
    if (timer) {
      return;
    }
    timer = setTimeout(() => {
      loadImage();
      timer = null;
    }, 100)
  }

  setTimeout(() => {
    loadImage();
  }, 100);
  if (!window.loadedImage) {
    document.addEventListener('scroll', scrollFn);
  }

  function loadImage() {
    const doc = document.documentElement;
    const scrollTop = doc.scrollTop;
    if (boxs.every(box => box.loadedImage)) {
      document.removeEventListener('scroll', scrollFn);
      window.loadedImage = 1;
      return;
    }
    boxs.forEach((box, idx) => {
      if (box.loadedImage) {
        return;
      }
      if (box.getBoundingClientRect().top < windowHeight) {
        const imgDom = box.querySelector('img');
        imgDom.src = imgDom.dataset.cover;
        // 图片成功加载
        imgDom.onload = () => {
          box.classList.remove('img-loading');
          box.loadedImage = true;
          if (!window.loadImageSet) {
            window.loadImageSet = [];
          }
          window.loadImageSet.push(idx);
        }
        // 图片加载失败
        imgDom.onerror = () => {
          box.classList.remove('img-loading');
          box.classList.add('img-error');
          imgDom.src = '/imgs/404.png';
        }
      }
    });
  }
</script>