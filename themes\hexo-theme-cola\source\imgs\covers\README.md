# 博客封面模板使用指南

## 可用的封面模板

本主题提供了以下固定尺寸的封面模板（400x260px）：

### 1. 技术分享类
- `tech-cover-1.svg` - 紫色渐变，适用于技术分享文章
- `tech-cover-2.svg` - 蓝色渐变，适用于开发教程文章

### 2. AI应用类
- `ai-cover.svg` - 粉橙渐变，适用于AI相关文章

### 3. 工具类
- `tools-cover.svg` - 青粉渐变，适用于工具介绍文章

### 4. 默认模板
- `default-cover.svg` - 橙色渐变，通用默认封面

## 使用方法

在文章的 Front Matter 中设置 cover 字段：

```yaml
---
title: 文章标题
cover: imgs/covers/tech-cover-1.svg
date: 2025-01-01
tags:
- 标签
categories:
- 分类
---
```

## 封面规格

- **尺寸**: 400x260px
- **格式**: SVG（矢量图，支持任意缩放）
- **设计**: 渐变背景 + 几何装饰元素 + 中文标题

## 自定义封面

如需自定义封面，请遵循以下规范：
1. 保持 400x260px 的宽高比例
2. 使用渐变背景提升视觉效果
3. 确保文字在白色背景下清晰可读
4. 避免过于复杂的设计元素

## 封面优化特性

- 统一的封面高度（260px）
- 懒加载支持
- 加载失败自动回退
- 响应式适配
- 鼠标悬停缩放效果
