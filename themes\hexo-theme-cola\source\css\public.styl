li { list-style: none; }
a { text-decoration: none; color: #333; }
* { box-sizing: border-box; margin: 0; padding: 0; user-select: none; }
html {
  // filter: grayscale(1);
  font-family: -apple-system,system-ui,Segoe UI,Roboto,Ubuntu,Cantarell,Noto Sans,sans-serif,BlinkMacSystemFont,"Helvetica Neue","PingFang SC","Hiragino Sans GB","Microsoft YaHei",Arial;
}
body {
  min-height: 100vh;
}
::-webkit-scrollbar {
  width: 0;
}

input {
  color: #666;
  padding: 7px 10px;
  outline: none;
  border: 1px solid #eee;
  font-size: 13px;
}



@import './mobile.styl';
