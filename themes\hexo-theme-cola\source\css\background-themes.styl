/* 博客背景主题优化 */

/* 默认主题 - 梦幻紫蓝 */
.container {
  /* 添加动态粒子效果 */
  position: relative;
  
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
      radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
      radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.3), transparent),
      radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.2), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 20s linear infinite;
    pointer-events: none;
    z-index: 1;
  }
}

@keyframes sparkle {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-100px); }
}

/* 主题1: 科技蓝 */
.theme-tech-blue .container {
  background:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(147, 197, 253, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(30, 58, 138, 0.2) 0%, transparent 60%),
    linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #93c5fd 100%) !important;
  background-size: 600px 600px, 400px 400px, 800px 800px, 100% 100% !important;
  background-position: 25% 25%, 75% 75%, 50% 10%, center !important;
}

.theme-tech-blue .header::after {
  background: linear-gradient(45deg, rgba(30, 58, 138, 0.9) 0%, rgba(59, 130, 246, 0.8) 100%) !important;
}

/* 主题2: 温暖橙 */
.theme-warm-orange .container {
  background:
    radial-gradient(circle at 30% 70%, rgba(251, 146, 60, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(254, 215, 170, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 50% 90%, rgba(234, 88, 12, 0.2) 0%, transparent 60%),
    linear-gradient(135deg, #ea580c 0%, #fb923c 50%, #fed7aa 100%) !important;
  background-size: 500px 500px, 300px 300px, 700px 700px, 100% 100% !important;
  background-position: 30% 70%, 70% 30%, 50% 90%, center !important;
}

.theme-warm-orange .header::after {
  background: linear-gradient(45deg, rgba(234, 88, 12, 0.9) 0%, rgba(251, 146, 60, 0.8) 100%) !important;
}

/* 主题3: 森林绿 */
.theme-forest-green .container {
  background:
    radial-gradient(circle at 40% 60%, rgba(34, 197, 94, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 60% 40%, rgba(187, 247, 208, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 20% 20%, rgba(22, 101, 52, 0.2) 0%, transparent 60%),
    linear-gradient(135deg, #166534 0%, #22c55e 50%, #bbf7d0 100%) !important;
  background-size: 450px 450px, 350px 350px, 600px 600px, 100% 100% !important;
  background-position: 40% 60%, 60% 40%, 20% 20%, center !important;
}

.theme-forest-green .header::after {
  background: linear-gradient(45deg, rgba(22, 101, 52, 0.9) 0%, rgba(34, 197, 94, 0.8) 100%) !important;
}

/* 主题4: 樱花粉 */
.theme-sakura-pink .container {
  background:
    radial-gradient(circle at 35% 65%, rgba(244, 114, 182, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 65% 35%, rgba(252, 231, 243, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(190, 24, 93, 0.2) 0%, transparent 60%),
    linear-gradient(135deg, #be185d 0%, #f472b6 50%, #fce7f3 100%) !important;
  background-size: 400px 400px, 250px 250px, 550px 550px, 100% 100% !important;
  background-position: 35% 65%, 65% 35%, 80% 80%, center !important;
}

.theme-sakura-pink .header::after {
  background: linear-gradient(45deg, rgba(190, 24, 93, 0.9) 0%, rgba(244, 114, 182, 0.8) 100%) !important;
}

/* 主题5: 深空紫 */
.theme-deep-space .container {
  background:
    radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.5) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(196, 181, 253, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(30, 27, 75, 0.6) 0%, transparent 70%),
    radial-gradient(circle at 10% 10%, rgba(139, 92, 246, 0.2) 0%, transparent 80%),
    linear-gradient(135deg, #1e1b4b 0%, #8b5cf6 50%, #c4b5fd 100%) !important;
  background-size: 700px 700px, 500px 500px, 300px 300px, 800px 800px, 100% 100% !important;
  background-position: 20% 80%, 80% 20%, 50% 50%, 10% 10%, center !important;
}

.theme-deep-space .header::after {
  background: linear-gradient(45deg, rgba(30, 27, 75, 0.95) 0%, rgba(139, 92, 246, 0.85) 100%) !important;
}

/* 主题6: 日落黄 */
.theme-sunset-yellow .container {
  background:
    radial-gradient(circle at 45% 55%, rgba(245, 158, 11, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 55% 45%, rgba(254, 240, 138, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 90% 10%, rgba(217, 119, 6, 0.2) 0%, transparent 60%),
    linear-gradient(135deg, #d97706 0%, #f59e0b 50%, #fef08a 100%) !important;
  background-size: 550px 550px, 400px 400px, 650px 650px, 100% 100% !important;
  background-position: 45% 55%, 55% 45%, 90% 10%, center !important;
}

.theme-sunset-yellow .header::after {
  background: linear-gradient(45deg, rgba(217, 119, 6, 0.9) 0%, rgba(245, 158, 11, 0.8) 100%) !important;
}

/* 增强卡片透明度和毛玻璃效果 */
.main-left--block,
.main-right--board,
.main-right--site,
.article,
.index-article {
  background-color: rgba(255, 255, 255, 0.92) !important;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 搜索框优化 */
.header-search--layer {
  background-color: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

/* 移动端优化 */
@media screen and (max-width: 768px) {
  .container::before {
    background-size: 100px 50px;
    animation-duration: 15s;
  }
  
  .main-left--block,
  .main-right--board,
  .main-right--site {
    background-color: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
}

/* 夜间模式适配 */
@media (prefers-color-scheme: dark) {
  .main-left--block,
  .main-right--board,
  .main-right--site,
  .article,
  .index-article {
    background-color: rgba(26, 26, 46, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.1);
    color: #e0e0e0;
  }
  
  .header-search--layer {
    background-color: rgba(26, 26, 46, 0.95) !important;
    color: #e0e0e0;
  }
}

/* 主题切换器样式 */
.theme-switcher {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 9999; /* 提高z-index确保在最顶层 */
}

.theme-toggle {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-width: 60px;
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: scale(1.05);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.theme-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.theme-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.theme-panel {
  position: absolute;
  right: 80px;
  top: 0;
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 16px;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  display: none;
  animation: slideIn 0.3s ease;
  z-index: 10000; /* 确保面板在最顶层 */
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.theme-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.theme-panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.theme-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.theme-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #666;
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.theme-option:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.theme-option.active {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.theme-preview {
  width: 50px;
  height: 32px;
  border-radius: 6px;
  margin-bottom: 6px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.theme-option:hover .theme-preview {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.theme-name {
  font-size: 11px;
  color: #666;
  font-weight: 500;
  text-align: center;
}

/* 主题预览样式 */
.theme-preview-default {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.theme-preview-tech-blue {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #93c5fd 100%);
}

.theme-preview-warm-orange {
  background: linear-gradient(135deg, #ea580c 0%, #fb923c 50%, #fed7aa 100%);
}

.theme-preview-forest-green {
  background: linear-gradient(135deg, #166534 0%, #22c55e 50%, #bbf7d0 100%);
}

.theme-preview-sakura-pink {
  background: linear-gradient(135deg, #be185d 0%, #ec4899 50%, #fce7f3 100%);
}

.theme-preview-deep-space {
  background: linear-gradient(135deg, #1e1b4b 0%, #8b5cf6 50%, #c4b5fd 100%);
}

.theme-preview-sunset-yellow {
  background: linear-gradient(135deg, #a16207 0%, #eab308 50%, #fef3c7 100%);
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .theme-switcher {
    right: 10px;
    top: auto;
    bottom: 20px;
    transform: none;
    /* 适配安全区域 */
    bottom: calc(20px + env(safe-area-inset-bottom));
  }

  .theme-panel {
    right: -10px;
    bottom: 80px;
    top: auto;
    width: 250px;
    max-width: calc(100vw - 20px); /* 确保不超出屏幕 */
    /* 适配安全区域，确保不被状态栏遮挡 */
    bottom: calc(80px + env(safe-area-inset-bottom));
    /* 如果面板高度超出可视区域，则调整位置 */
    max-height: calc(100vh - 120px - env(safe-area-inset-bottom) - env(safe-area-inset-top));
    overflow-y: auto;
    padding: 14px;
  }

  .theme-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .theme-preview {
    width: 42px;
    height: 28px;
  }
}

/* 小屏幕设备进一步优化 */
@media screen and (max-width: 480px) {
  .theme-switcher {
    right: 5px;
    bottom: calc(15px + env(safe-area-inset-bottom));
  }

  .theme-panel {
    right: -5px;
    width: 230px;
    bottom: calc(75px + env(safe-area-inset-bottom));
    padding: 12px;
    max-height: calc(100vh - 100px - env(safe-area-inset-bottom) - env(safe-area-inset-top));
  }

  .theme-grid {
    gap: 6px;
  }

  .theme-preview {
    width: 38px;
    height: 25px;
  }
}

/* 超小屏幕设备优化 */
@media screen and (max-width: 360px) {
  .theme-panel {
    width: 210px;
    right: 0;
    left: 50%;
    transform: translateX(-50%);
    bottom: calc(75px + env(safe-area-inset-bottom));
    padding: 10px;
  }
}

/* 横屏模式优化 */
@media screen and (max-width: 768px) and (orientation: landscape) {
  .theme-switcher {
    bottom: calc(10px + env(safe-area-inset-bottom));
  }

  .theme-panel {
    bottom: calc(60px + env(safe-area-inset-bottom));
    max-height: calc(100vh - 80px - env(safe-area-inset-bottom) - env(safe-area-inset-top));
  }
}
