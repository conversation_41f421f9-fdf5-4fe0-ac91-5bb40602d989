<svg width="400" height="260" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="techGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="260" fill="url(#techGradient1)"/>
  <circle cx="350" cy="50" r="30" fill="rgba(255,255,255,0.1)"/>
  <circle cx="50" cy="200" r="40" fill="rgba(255,255,255,0.1)"/>
  <rect x="20" y="20" width="60" height="4" fill="rgba(255,255,255,0.3)" rx="2"/>
  <rect x="20" y="30" width="40" height="4" fill="rgba(255,255,255,0.2)" rx="2"/>
  <text x="200" y="140" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">技术分享</text>
</svg>
