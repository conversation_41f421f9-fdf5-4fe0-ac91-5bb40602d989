<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>主题切换器调试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="http://localhost:4000/css/background-themes.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            min-height: 100vh;
        }
        
        .container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            padding: 20px;
            transition: background 0.5s ease;
        }
        
        .debug-info {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #5a6fd8;
        }
        
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="debug-info">
            <h1>主题切换器调试页面</h1>
            <p>这个页面用于测试主题切换功能是否正常工作。</p>
            
            <div>
                <button class="test-button" onclick="testThemeSwitch('default')">测试默认主题</button>
                <button class="test-button" onclick="testThemeSwitch('tech-blue')">测试科技蓝</button>
                <button class="test-button" onclick="testThemeSwitch('warm-orange')">测试温暖橙</button>
                <button class="test-button" onclick="testThemeSwitch('forest-green')">测试森林绿</button>
            </div>
            
            <div style="margin-top: 20px;">
                <button class="test-button" onclick="checkElements()">检查元素</button>
                <button class="test-button" onclick="clearConsole()">清空控制台</button>
            </div>
            
            <h3>控制台输出:</h3>
            <div id="console-output"></div>
        </div>
    </div>

    <!-- 主题切换器 -->
    <div id="theme-switcher" class="theme-switcher">
        <button id="theme-toggle" class="theme-toggle" title="切换主题">
            <span class="theme-icon">🎨</span>
            <span class="theme-text">主题</span>
        </button>
        <div id="theme-panel" class="theme-panel">
            <div class="theme-panel-header">
                <h3>选择主题</h3>
                <button id="theme-close" class="theme-close">&times;</button>
            </div>
            <div class="theme-grid">
                <div class="theme-option" data-theme="default">
                    <div class="theme-preview theme-preview-default"></div>
                    <span class="theme-name">梦幻紫蓝</span>
                </div>
                <div class="theme-option" data-theme="tech-blue">
                    <div class="theme-preview theme-preview-tech-blue"></div>
                    <span class="theme-name">科技蓝</span>
                </div>
                <div class="theme-option" data-theme="warm-orange">
                    <div class="theme-preview theme-preview-warm-orange"></div>
                    <span class="theme-name">温暖橙</span>
                </div>
                <div class="theme-option" data-theme="forest-green">
                    <div class="theme-preview theme-preview-forest-green"></div>
                    <span class="theme-name">森林绿</span>
                </div>
                <div class="theme-option" data-theme="sakura-pink">
                    <div class="theme-preview theme-preview-sakura-pink"></div>
                    <span class="theme-name">樱花粉</span>
                </div>
                <div class="theme-option" data-theme="deep-space">
                    <div class="theme-preview theme-preview-deep-space"></div>
                    <span class="theme-name">深空紫</span>
                </div>
                <div class="theme-option" data-theme="sunset-yellow">
                    <div class="theme-preview theme-preview-sunset-yellow"></div>
                    <span class="theme-name">日落黄</span>
                </div>
            </div>
        </div>
    </div>

    <script src="http://localhost:4000/js/theme-switcher.js"></script>
    
    <script>
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '✅';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        // 测试函数
        function testThemeSwitch(themeName) {
            console.log(`手动测试切换到主题: ${themeName}`);
            
            // 移除所有主题类
            const themeClasses = ['theme-tech-blue', 'theme-warm-orange', 'theme-forest-green', 'theme-sakura-pink', 'theme-deep-space', 'theme-sunset-yellow'];
            themeClasses.forEach(cls => document.body.classList.remove(cls));
            
            // 添加新主题类
            if (themeName !== 'default') {
                document.body.classList.add(`theme-${themeName}`);
            }
            
            console.log('切换后body类名:', document.body.className);
            
            // 检查容器背景
            setTimeout(() => {
                const container = document.querySelector('.container');
                if (container) {
                    const computedStyle = window.getComputedStyle(container);
                    console.log('容器背景样式:', computedStyle.background.substring(0, 100) + '...');
                }
            }, 100);
        }
        
        function checkElements() {
            console.log('=== 元素检查 ===');
            
            const switcher = document.getElementById('theme-switcher');
            console.log('主题切换器:', switcher ? '✅ 找到' : '❌ 未找到');
            
            if (switcher) {
                const toggle = switcher.querySelector('#theme-toggle');
                const panel = switcher.querySelector('#theme-panel');
                const options = switcher.querySelectorAll('.theme-option');
                
                console.log('切换按钮:', toggle ? '✅ 找到' : '❌ 未找到');
                console.log('主题面板:', panel ? '✅ 找到' : '❌ 未找到');
                console.log('主题选项数量:', options.length);
                
                if (toggle) {
                    console.log('按钮是否已初始化:', toggle.hasAttribute('data-theme-initialized') ? '✅ 是' : '❌ 否');
                }
            }
            
            console.log('当前主题:', localStorage.getItem('blog-theme') || 'default');
            console.log('body类名:', document.body.className);
        }
        
        function clearConsole() {
            consoleOutput.textContent = '';
        }
        
        // 页面加载完成后检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('页面加载完成，开始检查...');
                checkElements();
            }, 1000);
        });
    </script>
</body>
</html>
