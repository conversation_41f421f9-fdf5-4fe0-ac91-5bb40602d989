<head>
  <meta charset="utf-8">
  <%
    const baseTitle = config.title
    const pageTitle = page.title || '小站首页'
    const title = '千修' + pageTitle
  %>
  <title><% if (title){ %><%= title %> | <% } %><%= config.title %></title>
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="shortcut icon" href="<%- url_for('imgs/shortcut-icon.ico') %>" type="image/x-icon">
  <link rel="stylesheet" href="<%- url_for('css/public.css') %>" />
  <link rel="stylesheet" href="<%- url_for('css/layout.css') %>" />
  <link rel="stylesheet" href="<%- url_for('css/iconfont.css') %>" />
  <link rel="stylesheet" href="<%- url_for('css/fallback.css') %>" />
  <link rel="stylesheet" href="<%- url_for('css/background-themes.css') %>" />

  <!-- 预加载关键背景图片 -->
  <link rel="preload" href="<%- url_for('imgs/bg-cover.jpeg') %>" as="image" />
  <link rel="preload" href="<%- url_for('imgs/top-cover.jpeg') %>" as="image" />

  <!-- 音乐播放器CSS和JS已禁用 -->
  <!-- <link rel="stylesheet" href="<%- url_for('css/APlayer.min.css') %>" /> -->
  <!-- <script src="<%- url_for('js/APlayer.min.js') %>"></script> -->
  <script src="<%- url_for('js/jquery.min.js') %>"></script>
  <script src="<%- url_for('js/jquery.pjax.min.js') %>"></script>
  <script src="<%- url_for('js/resource-fallback.js') %>"></script>
  <script src="<%- url_for('js/theme-switcher.js') %>"></script>

  <script src='//unpkg.com/valine/dist/Valine.min.js'></script>
  <script>
    document.title = `<%- title %>`
  </script>
</head>